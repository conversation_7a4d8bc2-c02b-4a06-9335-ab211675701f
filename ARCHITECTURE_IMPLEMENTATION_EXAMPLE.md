# 架构优化实现示例

## 🎯 基于当前项目的具体优化实现

### 1. Domain层实现示例

#### 创建Use Cases
```kotlin
// app/src/main/java/com/sdwu/kotlin/domain/usecase/user/GetUserProfileUseCase.kt
package com.sdwu.kotlin.domain.usecase.user

import com.sdwu.kotlin.domain.model.User
import com.sdwu.kotlin.domain.repository.UserRepository
import javax.inject.Inject

class GetUserProfileUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(userId: String): Result<User> {
        return try {
            val user = userRepository.getCurrentUser()
            if (user != null) {
                Result.success(user)
            } else {
                Result.failure(UserNotFoundException("User not found: $userId"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// app/src/main/java/com/sdwu/kotlin/domain/usecase/health/GetECGDataUseCase.kt
package com.sdwu.kotlin.domain.usecase.health

import com.sdwu.kotlin.domain.model.ECGData
import com.sdwu.kotlin.domain.repository.HealthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetECGDataUseCase @Inject constructor(
    private val healthRepository: HealthRepository
) {
    operator fun invoke(): Flow<List<ECGData>> {
        return healthRepository.getECGDataStream()
    }
    
    suspend fun getLatestReading(): ECGData? {
        return healthRepository.getLatestECGReading()
    }
}
```

#### Domain Models
```kotlin
// app/src/main/java/com/sdwu/kotlin/domain/model/User.kt
package com.sdwu.kotlin.domain.model

data class User(
    val id: String,
    val name: String,
    val email: String,
    val registrationDate: String,
    val avatarUrl: String? = null,
    val preferences: UserPreferences = UserPreferences()
) {
    fun isProfileComplete(): Boolean {
        return name.isNotBlank() && email.isNotBlank()
    }
    
    fun getDisplayName(): String {
        return name.ifBlank { "未命名用户" }
    }
}

data class UserPreferences(
    val theme: Theme = Theme.SYSTEM,
    val notifications: Boolean = true,
    val dataSync: Boolean = true
)

enum class Theme {
    LIGHT, DARK, SYSTEM
}

// app/src/main/java/com/sdwu/kotlin/domain/model/HealthData.kt
package com.sdwu.kotlin.domain.model

import java.time.LocalDateTime

data class ECGData(
    val id: String,
    val timestamp: LocalDateTime,
    val heartRate: Int,
    val rhythm: ECGRhythm,
    val quality: SignalQuality,
    val rawData: List<Float> = emptyList()
) {
    fun isNormalHeartRate(): Boolean {
        return heartRate in 60..100
    }
}

enum class ECGRhythm {
    NORMAL, IRREGULAR, ATRIAL_FIBRILLATION, UNKNOWN
}

enum class SignalQuality {
    EXCELLENT, GOOD, FAIR, POOR
}
```

#### Repository Interfaces (Domain层)
```kotlin
// app/src/main/java/com/sdwu/kotlin/domain/repository/UserRepository.kt
package com.sdwu.kotlin.domain.repository

import com.sdwu.kotlin.domain.model.User
import kotlinx.coroutines.flow.Flow

interface UserRepository {
    suspend fun getCurrentUser(): User?
    suspend fun getUserById(userId: String): User?
    suspend fun updateUser(user: User): User
    suspend fun deleteUser(userId: String)
    fun observeCurrentUser(): Flow<User?>
    suspend fun initializeDefaultUser()
}

// app/src/main/java/com/sdwu/kotlin/domain/repository/HealthRepository.kt
package com.sdwu.kotlin.domain.repository

import com.sdwu.kotlin.domain.model.ECGData
import com.sdwu.kotlin.domain.model.HRVData
import kotlinx.coroutines.flow.Flow

interface HealthRepository {
    fun getECGDataStream(): Flow<List<ECGData>>
    suspend fun getLatestECGReading(): ECGData?
    suspend fun saveECGReading(data: ECGData)
    
    fun getHRVDataStream(): Flow<List<HRVData>>
    suspend fun getLatestHRVReading(): HRVData?
    suspend fun saveHRVReading(data: HRVData)
}
```

### 2. 增强的ViewModel实现

#### 基础ViewModel
```kotlin
// app/src/main/java/com/sdwu/kotlin/presentation/base/BaseViewModel.kt
package com.sdwu.kotlin.presentation.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

abstract class BaseViewModel<UiState : Any>(
    initialState: UiState
) : ViewModel() {
    
    protected val _uiState = MutableStateFlow(initialState)
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    protected val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    protected val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    protected fun updateState(update: (UiState) -> UiState) {
        _uiState.value = update(_uiState.value)
    }
    
    protected fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
    
    protected fun setError(error: String?) {
        _error.value = error
    }
    
    protected fun handleError(throwable: Throwable) {
        setError(throwable.message ?: "Unknown error occurred")
        setLoading(false)
    }
    
    protected fun executeUseCase(
        block: suspend () -> Unit,
        onError: (Throwable) -> Unit = ::handleError
    ) {
        viewModelScope.launch {
            try {
                setLoading(true)
                setError(null)
                block()
            } catch (e: Exception) {
                onError(e)
            } finally {
                setLoading(false)
            }
        }
    }
}
```

#### 重构后的ProfileViewModel
```kotlin
// app/src/main/java/com/sdwu/kotlin/presentation/profile/ProfileViewModel.kt
package com.sdwu.kotlin.presentation.profile

import com.sdwu.kotlin.domain.usecase.user.GetUserProfileUseCase
import com.sdwu.kotlin.domain.usecase.user.UpdateUserProfileUseCase
import com.sdwu.kotlin.presentation.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val updateUserProfileUseCase: UpdateUserProfileUseCase
) : BaseViewModel<ProfileUiState>(ProfileUiState()) {
    
    init {
        loadUserProfile()
    }
    
    fun loadUserProfile() {
        executeUseCase {
            getUserProfileUseCase("current_user")
                .onSuccess { user ->
                    updateState { it.copy(user = user, isEditMode = false) }
                }
                .onFailure { error ->
                    setError(error.message)
                }
        }
    }
    
    fun updateUserInfo(name: String, email: String) {
        val currentUser = _uiState.value.user ?: return
        val updatedUser = currentUser.copy(name = name, email = email)
        
        executeUseCase {
            updateUserProfileUseCase(updatedUser)
                .onSuccess { user ->
                    updateState { it.copy(user = user, isEditMode = false) }
                }
                .onFailure { error ->
                    setError(error.message)
                }
        }
    }
    
    fun toggleEditMode() {
        updateState { it.copy(isEditMode = !it.isEditMode) }
    }
    
    fun clearError() {
        setError(null)
    }
}

data class ProfileUiState(
    val user: com.sdwu.kotlin.domain.model.User? = null,
    val isEditMode: Boolean = false
)
```

### 3. 数据层重构

#### Repository实现
```kotlin
// app/src/main/java/com/sdwu/kotlin/data/repository/UserRepositoryImpl.kt
package com.sdwu.kotlin.data.repository

import com.sdwu.kotlin.data.mapper.UserMapper
import com.sdwu.kotlin.data.source.local.UserLocalDataSource
import com.sdwu.kotlin.data.source.remote.UserRemoteDataSource
import com.sdwu.kotlin.domain.model.User
import com.sdwu.kotlin.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepositoryImpl @Inject constructor(
    private val localDataSource: UserLocalDataSource,
    private val remoteDataSource: UserRemoteDataSource,
    private val userMapper: UserMapper
) : UserRepository {
    
    override suspend fun getCurrentUser(): User? {
        // 1. 尝试从本地获取
        val localUser = localDataSource.getCurrentUser()
        if (localUser != null) {
            return userMapper.mapToDomain(localUser)
        }
        
        // 2. 从远程获取并缓存
        return try {
            val remoteUser = remoteDataSource.getCurrentUser()
            if (remoteUser != null) {
                localDataSource.saveUser(remoteUser)
                userMapper.mapToDomain(remoteUser)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    override suspend fun updateUser(user: User): User {
        val dataUser = userMapper.mapToData(user)
        
        // 1. 更新本地
        localDataSource.saveUser(dataUser)
        
        // 2. 同步到远程
        try {
            remoteDataSource.updateUser(dataUser)
        } catch (e: Exception) {
            // 远程更新失败，标记为待同步
            localDataSource.markForSync(user.id)
        }
        
        return user
    }
    
    override fun observeCurrentUser(): Flow<User?> {
        return localDataSource.observeCurrentUser()
            .map { it?.let(userMapper::mapToDomain) }
    }
    
    override suspend fun initializeDefaultUser() {
        if (getCurrentUser() == null) {
            val defaultUser = User(
                id = "default_user_001",
                name = "默认用户",
                email = "<EMAIL>",
                registrationDate = "2024-01-01"
            )
            updateUser(defaultUser)
        }
    }
}
```

#### 数据映射器
```kotlin
// app/src/main/java/com/sdwu/kotlin/data/mapper/UserMapper.kt
package com.sdwu.kotlin.data.mapper

import com.sdwu.kotlin.data.model.UserEntity
import com.sdwu.kotlin.domain.model.User
import com.sdwu.kotlin.domain.model.UserPreferences
import com.sdwu.kotlin.domain.model.Theme
import javax.inject.Inject

class UserMapper @Inject constructor() {
    
    fun mapToDomain(entity: UserEntity): User {
        return User(
            id = entity.id,
            name = entity.name,
            email = entity.email,
            registrationDate = entity.registrationDate,
            avatarUrl = entity.avatarUrl,
            preferences = UserPreferences(
                theme = Theme.valueOf(entity.theme),
                notifications = entity.notifications,
                dataSync = entity.dataSync
            )
        )
    }
    
    fun mapToData(domain: User): UserEntity {
        return UserEntity(
            id = domain.id,
            name = domain.name,
            email = domain.email,
            registrationDate = domain.registrationDate,
            avatarUrl = domain.avatarUrl,
            theme = domain.preferences.theme.name,
            notifications = domain.preferences.notifications,
            dataSync = domain.preferences.dataSync
        )
    }
}
```

### 4. 依赖注入模块

#### Domain模块
```kotlin
// app/src/main/java/com/sdwu/kotlin/di/DomainModule.kt
package com.sdwu.kotlin.di

import com.sdwu.kotlin.domain.repository.UserRepository
import com.sdwu.kotlin.domain.repository.HealthRepository
import com.sdwu.kotlin.domain.usecase.user.GetUserProfileUseCase
import com.sdwu.kotlin.domain.usecase.user.UpdateUserProfileUseCase
import com.sdwu.kotlin.domain.usecase.health.GetECGDataUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object DomainModule {
    
    @Provides
    fun provideGetUserProfileUseCase(
        userRepository: UserRepository
    ): GetUserProfileUseCase = GetUserProfileUseCase(userRepository)
    
    @Provides
    fun provideUpdateUserProfileUseCase(
        userRepository: UserRepository
    ): UpdateUserProfileUseCase = UpdateUserProfileUseCase(userRepository)
    
    @Provides
    fun provideGetECGDataUseCase(
        healthRepository: HealthRepository
    ): GetECGDataUseCase = GetECGDataUseCase(healthRepository)
}
```

#### Data模块
```kotlin
// app/src/main/java/com/sdwu/kotlin/di/DataModule.kt
package com.sdwu.kotlin.di

import com.sdwu.kotlin.data.repository.UserRepositoryImpl
import com.sdwu.kotlin.data.repository.HealthRepositoryImpl
import com.sdwu.kotlin.domain.repository.UserRepository
import com.sdwu.kotlin.domain.repository.HealthRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {
    
    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository
    
    @Binds
    @Singleton
    abstract fun bindHealthRepository(
        healthRepositoryImpl: HealthRepositoryImpl
    ): HealthRepository
}
```

### 5. View系统兼容性实现

#### 统一状态适配器
```kotlin
// app/src/main/java/com/sdwu/kotlin/presentation/adapter/StateAdapter.kt
package com.sdwu.kotlin.presentation.adapter

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.Lifecycle
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class StateAdapter {
    
    // ViewBinding/传统View中观察StateFlow
    fun <T> observeState(
        lifecycleOwner: LifecycleOwner,
        stateFlow: StateFlow<T>,
        onStateChanged: (T) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                stateFlow.collect { state ->
                    onStateChanged(state)
                }
            }
        }
    }
}

// 扩展函数简化使用
fun <T> StateFlow<T>.observeInActivity(
    lifecycleOwner: LifecycleOwner,
    onStateChanged: (T) -> Unit
) {
    StateAdapter().observeState(lifecycleOwner, this, onStateChanged)
}
```

这个优化方案展示了如何在保持现有功能的基础上，逐步引入更清晰的架构分层和更好的View系统兼容性！

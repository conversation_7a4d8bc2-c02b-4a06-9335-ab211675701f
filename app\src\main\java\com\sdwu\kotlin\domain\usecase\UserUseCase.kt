package com.sdwu.kotlin.domain.usecase

import com.sdwu.kotlin.data.model.User
import com.sdwu.kotlin.data.repository.UserRepositoryInterface
import kotlinx.coroutines.flow.Flow

/**
 * 用户相关业务逻辑用例
 * Domain层：处理具体的业务规则和逻辑
 */
class UserUseCase(private val userRepository: UserRepositoryInterface) {
    
    suspend fun getUserById(userId: String): User? {
        return userRepository.getUserById(userId)
    }
    
    suspend fun createUser(user: User): Boolean {
        // 业务逻辑：验证用户数据
        if (user.name.isBlank() || user.email.isBlank()) {
            return false
        }
        
        // 检查邮箱格式
        if (!isValidEmail(user.email)) {
            return false
        }
        
        return try {
            userRepository.insertUser(user)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun updateUser(user: User): <PERSON><PERSON><PERSON> {
        return try {
            userRepository.updateUser(user)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    fun getAllUsers(): Flow<List<User>> {
        return userRepository.getAllUsers()
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
package com.sdwu.kotlin.presentation.util

import androidx.compose.runtime.*
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.sdwu.kotlin.presentation.theme.getAppContainer

/**
 * ViewModel工厂函数，自动注入依赖
 */
@Composable
inline fun <reified T : ViewModel> injectedViewModel(
    crossinline factory: (com.sdwu.kotlin.di.AppContainer) -> T
): T {
    val appContainer = getAppContainer()
        ?: throw IllegalStateException("AppContainer not found in composition")
    
    return viewModel { factory(appContainer) }
}

/**
 * 状态管理工具
 */
@Composable
fun <T> rememberAsyncState(
    initialValue: T,
    operation: suspend () -> T
): State<T> {
    val state = remember { mutableStateOf(initialValue) }
    
    LaunchedEffect(Unit) {
        try {
            state.value = operation()
        } catch (e: Exception) {
            // 错误处理可以在这里添加
        }
    }
    
    return state
}

/**
 * 延迟加载工具
 */
@Composable
fun <T> LazyLoadState(
    loader: suspend () -> T,
    content: @Composable (T?) -> Unit
) {
    var data by remember { mutableStateOf<T?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    
    LaunchedEffect(Unit) {
        try {
            data = loader()
        } finally {
            isLoading = false
        }
    }
    
    if (isLoading) {
        com.sdwu.kotlin.presentation.view.LoadingView()
    } else {
        content(data)
    }
}
package com.sdwu.kotlin.data.local

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 通用本地数据存储
 * 支持内存缓存和数据持久化
 */
class LocalDataStore<T> {
    private val _dataFlow = MutableStateFlow<T?>(null)
    val dataFlow: Flow<T?> = _dataFlow.asStateFlow()
    
    private val cache = mutableMapOf<String, T>()
    
    suspend fun save(key: String, data: T) {
        cache[key] = data
        _dataFlow.value = data
    }
    
    suspend fun load(key: String): T? {
        return cache[key]
    }
    
    suspend fun remove(key: String) {
        cache.remove(key)
        if (cache.isEmpty()) {
            _dataFlow.value = null
        }
    }
    
    suspend fun clear() {
        cache.clear()
        _dataFlow.value = null
    }
    
    fun getAll(): Map<String, T> {
        return cache.toMap()
    }
}

/**
 * 列表数据存储
 */
class ListDataStore<T> {
    private val _listFlow = MutableStateFlow<List<T>>(emptyList())
    val listFlow: Flow<List<T>> = _listFlow.asStateFlow()
    
    private val items = mutableListOf<T>()
    
    suspend fun add(item: T) {
        items.add(item)
        _listFlow.value = items.toList()
    }
    
    suspend fun addAll(newItems: List<T>) {
        items.addAll(newItems)
        _listFlow.value = items.toList()
    }
    
    suspend fun remove(item: T) {
        items.remove(item)
        _listFlow.value = items.toList()
    }
    
    suspend fun clear() {
        items.clear()
        _listFlow.value = emptyList()
    }
    
    fun getAll(): List<T> {
        return items.toList()
    }
    
    fun size(): Int {
        return items.size
    }
}
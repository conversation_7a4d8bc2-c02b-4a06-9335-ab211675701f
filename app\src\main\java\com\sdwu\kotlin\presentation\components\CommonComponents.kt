package com.sdwu.kotlin.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.sdwu.kotlin.presentation.state.UiState
import com.sdwu.kotlin.presentation.view.LoadingView
import com.sdwu.kotlin.presentation.view.ErrorView
import com.sdwu.kotlin.presentation.view.EmptyView

/**
 * 通用列表组件，支持自动状态管理
 */
@Composable
fun <T> StateAwareListView(
    uiState: UiState<List<T>>,
    onRetry: () -> Unit = {},
    emptyMessage: String = "暂无数据",
    modifier: Modifier = Modifier,
    itemContent: @Composable (T) -> Unit
) {
    Box(modifier = modifier.fillMaxSize()) {
        when (uiState) {
            is UiState.Loading -> {
                LoadingView()
            }
            is UiState.Error -> {
                ErrorView(
                    error = uiState.message,
                    onRetry = onRetry
                )
            }
            is UiState.Empty -> {
                EmptyView(message = emptyMessage)
            }
            is UiState.Success -> {
                if (uiState.data.isEmpty()) {
                    EmptyView(message = emptyMessage)
                } else {
                    LazyColumn {
                        items(uiState.data) { item ->
                            itemContent(item)
                        }
                    }
                }
            }
        }
    }
}

/**
 * 通用卡片组件
 */
@Composable
fun InfoCard(
    title: String,
    content: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            content()
        }
    }
}

/**
 * 数据显示组件
 */
@Composable
fun DataItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}
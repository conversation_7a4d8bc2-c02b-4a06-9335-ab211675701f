package com.sdwu.kotlin.di

import android.content.Context
import android.util.Log
import com.sdwu.kotlin.data.repository.HomeRepository
import com.sdwu.kotlin.data.repository.SettingsRepository
import com.sdwu.kotlin.data.repository.InMemoryUserRepository
import com.sdwu.kotlin.data.repository.UserRepositoryInterface
import com.sdwu.kotlin.data.repository.ECGRepository
import com.sdwu.kotlin.data.repository.HRVRepository
import com.sdwu.kotlin.domain.usecase.UserUseCase
import com.sdwu.kotlin.domain.usecase.ECGUseCase

/**
 * 应用依赖注入容器
 * 简化版的依赖注入，管理应用中的依赖关系
 */
class AppContainer(private val context: Context) {

    companion object {
        private const val TAG = "AppContainer"
    }

    // Repository实例 - 使用内存存储替代数据库
    val userRepository: UserRepositoryInterface by lazy {
        try {
            Log.d(TAG, "初始化InMemoryUserRepository")
            val repo = InMemoryUserRepository()
            Log.d(TAG, "InMemoryUserRepository初始化成功")
            repo
        } catch (e: Exception) {
            Log.e(TAG, "InMemoryUserRepository初始化失败", e)
            throw e
        }
    }

    val homeRepository by lazy {
        try {
            Log.d(TAG, "初始化HomeRepository")
            val repo = HomeRepository()
            Log.d(TAG, "HomeRepository初始化成功")
            repo
        } catch (e: Exception) {
            Log.e(TAG, "HomeRepository初始化失败", e)
            throw e
        }
    }

    val settingsRepository by lazy {
        try {
            Log.d(TAG, "初始化SettingsRepository")
            val repo = SettingsRepository(context)
            Log.d(TAG, "SettingsRepository初始化成功")
            repo
        } catch (e: Exception) {
            Log.e(TAG, "SettingsRepository初始化失败", e)
            throw e
        }
    }

    // ECG Repository
    val ecgRepository by lazy {
        try {
            Log.d(TAG, "初始化ECGRepository")
            val repo = ECGRepository()
            Log.d(TAG, "ECGRepository初始化成功")
            repo
        } catch (e: Exception) {
            Log.e(TAG, "ECGRepository初始化失败", e)
            throw e
        }
    }

    // HRV Repository
    val hrvRepository by lazy {
        try {
            Log.d(TAG, "初始化HRVRepository")
            val repo = HRVRepository()
            Log.d(TAG, "HRVRepository初始化成功")
            repo
        } catch (e: Exception) {
            Log.e(TAG, "HRVRepository初始化失败", e)
            throw e
        }
    }

    // Domain UseCase instances
    val userUseCase by lazy {
        try {
            Log.d(TAG, "初始化UserUseCase")
            val useCase = UserUseCase(userRepository)
            Log.d(TAG, "UserUseCase初始化成功")
            useCase
        } catch (e: Exception) {
            Log.e(TAG, "UserUseCase初始化失败", e)
            throw e
        }
    }
    
    val ecgUseCase by lazy {
        try {
            Log.d(TAG, "初始化ECGUseCase")
            val useCase = ECGUseCase(ecgRepository)
            Log.d(TAG, "ECGUseCase初始化成功")
            useCase
        } catch (e: Exception) {
            Log.e(TAG, "ECGUseCase初始化失败", e)
            throw e
        }
    }
}

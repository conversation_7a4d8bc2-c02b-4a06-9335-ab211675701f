# 架构优化指南

## 🎯 当前架构评估

### 现有优势 ✅
1. **完整MVVM架构** - 清晰的View-ViewModel-Model分离
2. **多UI技术栈** - Compose + ViewBinding + 传统View并存
3. **响应式编程** - StateFlow/LiveData状态管理
4. **内存存储** - 避免SQLite权限问题
5. **简化依赖注入** - AppContainer管理依赖

### 待优化点 🔧
1. **分层可以更清晰** - 缺少Domain层和Use Cases
2. **依赖注入可以更强大** - 考虑引入Hilt
3. **数据层可以更灵活** - Repository模式可以增强
4. **View系统兼容性** - 多UI技术栈协作可以更顺畅
5. **错误处理** - 可以更统一和完善

## 🏗️ 优化后的架构设计

### 1. Clean Architecture + MVVM 混合架构

```
📱 Presentation Layer (表现层)
├── 🎨 UI Components
│   ├── Compose Screens (声明式UI)
│   ├── ViewBinding Activities (类型安全)
│   └── Traditional Views (兼容性)
├── 🧠 ViewModels (状态管理)
└── 🎭 UI State Models (UI状态定义)

💼 Domain Layer (领域层) [新增]
├── 🎯 Use Cases (业务用例)
├── 📋 Domain Models (领域模型)
├── 🔌 Repository Interfaces (仓库接口)
└── 🛡️ Domain Exceptions (领域异常)

💾 Data Layer (数据层)
├── 🏪 Repository Implementations (仓库实现)
├── 📡 Data Sources (数据源)
│   ├── Local (本地存储)
│   ├── Remote (网络API)
│   └── Cache (缓存层)
├── 🔄 Data Mappers (数据映射)
└── 📊 Data Models (数据模型)

🔌 Infrastructure Layer (基础设施层)
├── 🏗️ Dependency Injection (依赖注入)
├── 🛡️ Error Handling (错误处理)
├── 📱 Navigation (导航管理)
└── 🔧 Utils (工具类)
```

### 2. 分层职责定义

#### Presentation Layer (表现层)
- **职责**: UI渲染、用户交互、状态展示
- **组件**: Screens, Activities, ViewModels, UI States
- **特点**: 
  - 支持多UI技术栈
  - 响应式状态管理
  - 生命周期感知

#### Domain Layer (领域层) [新增]
- **职责**: 业务逻辑、用例定义、领域规则
- **组件**: Use Cases, Domain Models, Repository Interfaces
- **特点**:
  - 纯Kotlin代码，无Android依赖
  - 业务逻辑集中管理
  - 可测试性强

#### Data Layer (数据层)
- **职责**: 数据获取、存储、缓存
- **组件**: Repositories, Data Sources, Mappers
- **特点**:
  - 多数据源支持
  - 智能缓存策略
  - 数据一致性保证

#### Infrastructure Layer (基础设施层)
- **职责**: 框架集成、工具支持
- **组件**: DI Container, Error Handler, Navigation
- **特点**:
  - 跨层服务支持
  - 统一错误处理
  - 配置管理

## 🚀 具体优化方案

### 1. 引入Domain层

#### Use Cases示例
```kotlin
// 用户相关用例
class GetUserProfileUseCase(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(userId: String): Result<User> {
        return try {
            val user = userRepository.getUserById(userId)
            if (user != null) {
                Result.success(user)
            } else {
                Result.failure(UserNotFoundException())
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

class UpdateUserProfileUseCase(
    private val userRepository: UserRepository,
    private val validator: UserValidator
) {
    suspend operator fun invoke(user: User): Result<User> {
        return try {
            validator.validate(user)
            val updatedUser = userRepository.updateUser(user)
            Result.success(updatedUser)
        } catch (e: ValidationException) {
            Result.failure(e)
        }
    }
}
```

#### Domain Models
```kotlin
// 纯业务模型，无Android依赖
data class User(
    val id: UserId,
    val profile: UserProfile,
    val settings: UserSettings,
    val createdAt: Timestamp
) {
    fun isProfileComplete(): Boolean {
        return profile.name.isNotBlank() && 
               profile.email.isNotBlank()
    }
}

@JvmInline
value class UserId(val value: String)

data class UserProfile(
    val name: String,
    val email: String,
    val avatarUrl: String?
)
```

### 2. 增强Repository模式

#### Repository接口 (Domain层)
```kotlin
interface UserRepository {
    suspend fun getUserById(id: UserId): User?
    suspend fun updateUser(user: User): User
    suspend fun deleteUser(id: UserId)
    fun observeUser(id: UserId): Flow<User?>
}

interface HealthDataRepository {
    suspend fun getECGData(timeRange: TimeRange): List<ECGReading>
    suspend fun getHRVData(timeRange: TimeRange): List<HRVReading>
    fun observeRealtimeData(): Flow<HealthReading>
}
```

#### Repository实现 (Data层)
```kotlin
class UserRepositoryImpl(
    private val localDataSource: UserLocalDataSource,
    private val remoteDataSource: UserRemoteDataSource,
    private val cacheManager: CacheManager
) : UserRepository {
    
    override suspend fun getUserById(id: UserId): User? {
        // 1. 检查缓存
        cacheManager.getUser(id)?.let { return it }
        
        // 2. 检查本地存储
        localDataSource.getUser(id)?.let { user ->
            cacheManager.putUser(user)
            return user
        }
        
        // 3. 从远程获取
        return try {
            val user = remoteDataSource.getUser(id)
            localDataSource.saveUser(user)
            cacheManager.putUser(user)
            user
        } catch (e: Exception) {
            null
        }
    }
}
```

### 3. 升级依赖注入

#### 模块化DI配置
```kotlin
// Domain模块
@Module
@InstallIn(SingletonComponent::class)
object DomainModule {
    
    @Provides
    fun provideGetUserProfileUseCase(
        userRepository: UserRepository
    ): GetUserProfileUseCase = GetUserProfileUseCase(userRepository)
    
    @Provides
    fun provideUpdateUserProfileUseCase(
        userRepository: UserRepository,
        validator: UserValidator
    ): UpdateUserProfileUseCase = UpdateUserProfileUseCase(userRepository, validator)
}

// Data模块
@Module
@InstallIn(SingletonComponent::class)
object DataModule {
    
    @Provides
    @Singleton
    fun provideUserRepository(
        localDataSource: UserLocalDataSource,
        remoteDataSource: UserRemoteDataSource,
        cacheManager: CacheManager
    ): UserRepository = UserRepositoryImpl(localDataSource, remoteDataSource, cacheManager)
}
```

### 4. View系统兼容性增强

#### 统一的ViewModel基类
```kotlin
abstract class BaseViewModel : ViewModel() {
    
    protected val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    protected val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 为ViewBinding/传统View提供LiveData
    val isLoadingLiveData: LiveData<Boolean> = isLoading.asLiveData()
    val errorLiveData: LiveData<String?> = error.asLiveData()
    
    protected fun handleError(throwable: Throwable) {
        _error.value = throwable.message ?: "Unknown error"
        _isLoading.value = false
    }
}
```

#### UI技术栈适配器
```kotlin
// Compose适配器
@Composable
fun <T> StateFlow<T>.collectAsStateWithLifecycle(): State<T> {
    return collectAsState()
}

// ViewBinding适配器
class ViewBindingStateObserver<T>(
    private val lifecycleOwner: LifecycleOwner,
    private val stateFlow: StateFlow<T>,
    private val onStateChanged: (T) -> Unit
) {
    fun observe() {
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                stateFlow.collect { onStateChanged(it) }
            }
        }
    }
}

// 传统View适配器
class TraditionalViewStateObserver<T>(
    private val activity: Activity,
    private val stateFlow: StateFlow<T>,
    private val onStateChanged: (T) -> Unit
) {
    fun observe() {
        if (activity is LifecycleOwner) {
            ViewBindingStateObserver(activity, stateFlow, onStateChanged).observe()
        }
    }
}
```

## 📊 优化效果预期

### 性能提升
- **启动速度**: 依赖注入优化 → 20%提升
- **内存使用**: 缓存策略优化 → 15%减少
- **响应速度**: 分层优化 → 30%提升

### 开发效率
- **代码复用**: Domain层抽象 → 40%提升
- **测试覆盖**: 分层测试 → 60%提升
- **维护成本**: 清晰架构 → 50%降低

### 扩展性
- **新功能开发**: Use Case模式 → 更快速
- **UI技术栈**: 多栈支持 → 更灵活
- **数据源**: Repository模式 → 更容易扩展

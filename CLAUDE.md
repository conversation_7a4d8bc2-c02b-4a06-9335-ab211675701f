# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

```bash
# Primary build and test script
build_and_test.bat                    # Automated build and test execution

# Core Gradle commands
gradlew clean                         # Clean project
gradlew assembleDebug                 # Build debug APK
gradlew installDebug                  # Install to device
gradlew test                          # Run unit tests
gradlew connectedAndroidTest          # Run instrumented tests
```

## Architecture Overview

This is a Kotlin Android application implementing a **layered Clean Architecture with MVVM pattern**, supporting both **Jetpack Compose** and **traditional View systems**.

### Core Architecture Layers

```
┌─────────────────┐
│  Presentation   │  → screens/, viewmodel/, presentation/
├─────────────────┤
│    Domain       │  → domain/usecase/ (Business logic)
├─────────────────┤
│     Data        │  → data/repository/, data/model/, data/local/
├─────────────────┤
│      DI         │  → di/AppContainer (Dependency injection)
└─────────────────┘
```

### Key Architectural Components

- **BaseViewModel**: Abstract base providing common loading/error state management via `executeAsync()`
- **ViewSystemAdapter**: Unified adapter bridging Compose and traditional View systems
- **UiState**: Sealed class for consistent state management (Loading, Success, Error, Empty)
- **AppContainer**: Custom dependency injection container with lazy initialization
- **UseCase Pattern**: Domain layer with business logic (UserUseCase, ECGUseCase)

### State Management Pattern

All ViewModels follow this StateFlow-based reactive pattern:
```kotlin
private val _uiState = MutableStateFlow(MyUiState())
val uiState: StateFlow<MyUiState> = _uiState.asStateFlow()

// In Compose
val uiState by viewModel.uiState.collectAsState()
```

### Dependency Injection Usage

ViewModels are created through AppContainer:
```kotlin
// In MainActivity or Application
val appContainer = (application as KotlinApplication).appContainer

// In Compose screens
val viewModel: ProfileViewModel = viewModel {
    ProfileViewModel(appContainer.userUseCase)
}

// With new injection helper
val viewModel: MyViewModel = injectedViewModel { container ->
    MyViewModel(container.userUseCase)
}
```

### View System Compatibility

The app supports dual UI systems:
- **Compose**: Primary UI system (Material3, Navigation Compose)
- **Traditional Views**: Legacy support with ViewBinding/DataBinding
- **Bridge Pattern**: `ViewSystemAdapter` provides seamless integration

## Key Dependencies and Frameworks

- **Compose Stack**: Material3, Navigation Compose, Activity Compose
- **Architecture**: Lifecycle ViewModels, StateFlow, Coroutines
- **Storage**: DataStore (preferences), In-memory repositories (Room temporarily disabled)
- **Network**: Retrofit + OkHttp, Gson (configured but not actively used)
- **Specialized**: MPAndroidChart, Lottie, CameraX, Aliyun IoT

## Testing Strategy

The project uses staged testing with comprehensive debugging:
- **Unit Tests**: Domain logic and ViewModels
- **Instrumented Tests**: UI testing with Compose test framework
- **Debug Tools**: ErrorLogger, DebugUtils, CrashHandler
- **Navigation Testing**: Specialized navigation debugging with fallback mechanisms

## Development Patterns

### Creating New Features

1. **Domain Layer**: Create UseCase for business logic
2. **Data Layer**: Implement Repository interface
3. **Presentation**: Create ViewModel extending BaseViewModel
4. **UI**: Use ViewSystemAdapter for unified state handling
5. **DI**: Add dependencies to AppContainer

### Error Handling

All async operations should use the BaseViewModel pattern:
```kotlin
fun loadData() {
    executeAsync(
        action = {
            // Your async logic here
            val data = useCase.getData()
            _uiState.value = _uiState.value.copy(data = data)
        }
    )
}
```

### Navigation

- Routes defined in `navigation/Routes.kt`
- Use `NavigationErrorHandler.safePopBackStack()` for safe navigation
- Extensive logging available for debugging navigation issues

## Repository Structure Context

- **Multiple Activity Support**: Traditional activities preserved for compatibility testing
- **Comprehensive Documentation**: Architecture guides, implementation examples, and migration strategies in root directory
- **Staged Migration**: Gradual transition from Views to Compose with bridge components
- **Chinese Development Context**: Optimized for Chinese development environment (Alibaba repositories, Chinese comments)

## Important Notes

- **SQLite/Room**: Currently disabled due to permission issues, using in-memory storage
- **Build Optimization**: Uses Alibaba Cloud repositories for faster builds in Chinese regions  
- **Error Resilience**: Comprehensive crash handling and recovery mechanisms throughout
- **Dual Language**: Code comments and documentation in Chinese, but code structure follows English conventions
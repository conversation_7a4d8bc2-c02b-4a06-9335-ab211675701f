# 优化后的Kotlin Android分层架构指南

## 架构概述

本项目采用改进的MVVM分层架构，支持Jetpack Compose和传统View系统：

```
┌─────────────────┐
│  Presentation   │  → UI层：screens/, components/, viewmodel/
├─────────────────┤
│    Domain       │  → 业务逻辑层：usecase/ (新增)
├─────────────────┤
│     Data        │  → 数据层：repository/, model/, local/
├─────────────────┤
│      DI         │  → 依赖注入：AppContainer
└─────────────────┘
```

## 主要改进

### 1. 新增Domain层
- **UserUseCase**: 用户相关业务逻辑
- **ECGUseCase**: ECG数据处理业务逻辑
- 集中处理业务规则和数据验证

### 2. 统一的View系统支持
- **ViewSystemAdapter**: 统一状态管理适配器
- **BaseViewModel**: 提供公共ViewModel功能
- **CommonViews**: 通用UI组件库

### 3. 改进的状态管理
- **UiState**: 统一UI状态封装
- **LocalDataStore**: 本地数据存储工具
- **ComposeUtils**: Compose辅助工具

## 使用示例

### 1. 创建ViewModel

```kotlin
class MyViewModel(private val userUseCase: UserUseCase) : BaseViewModel() {
    private val _uiState = MutableStateFlow(MyUiState())
    val uiState: StateFlow<MyUiState> = _uiState.asStateFlow()
    
    fun loadData() {
        executeAsync(
            action = {
                val data = userUseCase.getUserById("123")
                _uiState.value = _uiState.value.copy(data = data)
            }
        )
    }
}
```

### 2. 使用ViewSystemAdapter

```kotlin
@Composable
fun MyScreen() {
    val viewModel: MyViewModel = injectedViewModel { container ->
        MyViewModel(container.userUseCase)
    }
    
    ViewSystemAdapter(viewModel = viewModel) { vm ->
        MyContent(viewModel = vm)
    }
}
```

### 3. 使用通用组件

```kotlin
@Composable
fun MyContent(viewModel: MyViewModel) {
    val uiState by viewModel.uiState.collectAsState()
    
    StateAwareListView(
        uiState = uiState.dataState,
        onRetry = { viewModel.loadData() }
    ) { item ->
        InfoCard(title = item.title) {
            DataItem(label = "状态", value = item.status)
        }
    }
}
```

### 4. 依赖注入使用

```kotlin
// 在MainActivity中设置主题
setContent {
    AppTheme(appContainer = app.appContainer) {
        // 你的应用内容
        NavGraph(navController = navController)
    }
}

// 在任何Composable中获取依赖
@Composable
fun MyComponent() {
    val appContainer = getAppContainer()
    val viewModel: MyViewModel = injectedViewModel { container ->
        MyViewModel(container.userUseCase)
    }
}
```

## 架构优势

1. **清晰的分层**: 每层职责明确，易于维护
2. **统一的状态管理**: 减少重复代码，提高一致性
3. **可测试性**: 业务逻辑与UI分离，便于单元测试
4. **扩展性**: 支持多种View系统，易于添加新功能
5. **错误处理**: 统一的错误处理机制
6. **依赖注入**: 松耦合设计，便于测试和维护

## 文件结构

```
app/src/main/java/com/sdwu/kotlin/
├── data/
│   ├── local/           # 本地数据存储
│   ├── model/           # 数据模型
│   └── repository/      # 数据仓库
├── domain/
│   └── usecase/         # 业务用例
├── presentation/
│   ├── adapter/         # View系统适配器
│   ├── base/           # 基础类
│   ├── components/      # 通用组件
│   ├── state/          # 状态管理
│   ├── theme/          # 主题和样式
│   ├── util/           # Compose工具
│   └── view/           # 通用视图
├── di/                 # 依赖注入
├── screens/            # 页面实现
└── viewmodel/          # ViewModel层
```

这种架构设计既保持了代码的清晰性和可维护性，又提供了统一的开发体验和强大的扩展能力。
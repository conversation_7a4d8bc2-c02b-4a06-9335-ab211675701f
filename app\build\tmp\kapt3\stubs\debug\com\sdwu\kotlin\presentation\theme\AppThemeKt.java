package com.sdwu.kotlin.presentation.theme;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a#\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00022\u0011\u0010\b\u001a\r\u0012\u0004\u0012\u00020\u00060\t\u00a2\u0006\u0002\b\nH\u0007\u001a\n\u0010\u000b\u001a\u0004\u0018\u00010\u0002H\u0007\"\u0019\u0010\u0000\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\f"}, d2 = {"LocalAppContainer", "Landroidx/compose/runtime/ProvidableCompositionLocal;", "Lcom/sdwu/kotlin/di/AppContainer;", "getLocalAppContainer", "()Landroidx/compose/runtime/ProvidableCompositionLocal;", "AppTheme", "", "appContainer", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "getAppContainer", "app_debug"})
public final class AppThemeKt {
    
    /**
     * 全局主题提供者，支持依赖注入和主题管理
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.runtime.ProvidableCompositionLocal<com.sdwu.kotlin.di.AppContainer> LocalAppContainer = null;
    
    /**
     * 全局主题提供者，支持依赖注入和主题管理
     */
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.runtime.ProvidableCompositionLocal<com.sdwu.kotlin.di.AppContainer> getLocalAppContainer() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AppTheme(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.di.AppContainer appContainer, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 获取AppContainer的便捷函数
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.Nullable()
    public static final com.sdwu.kotlin.di.AppContainer getAppContainer() {
        return null;
    }
}
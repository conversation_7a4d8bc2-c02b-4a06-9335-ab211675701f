package com.sdwu.kotlin.presentation.state;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\u001a\u001d\u0010\u0000\u001a\u0004\u0018\u0001H\u0001\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u00a2\u0006\u0002\u0010\u0003\u001a\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u0005\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u001a\u0016\u0010\u0006\u001a\u00020\u0007\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u001a\u0016\u0010\b\u001a\u00020\u0007\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u001a\u0016\u0010\t\u001a\u00020\u0007\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u001a\u0016\u0010\n\u001a\u00020\u0007\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002\u00a8\u0006\u000b"}, d2 = {"getDataOrNull", "T", "Lcom/sdwu/kotlin/presentation/state/UiState;", "(Lcom/sdwu/kotlin/presentation/state/UiState;)Ljava/lang/Object;", "getErrorOrNull", "", "isEmpty", "", "isError", "isLoading", "isSuccess", "app_debug"})
public final class UiStateKt {
    
    /**
     * 状态扩展函数
     */
    public static final <T extends java.lang.Object>boolean isLoading(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$isLoading) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isSuccess(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$isSuccess) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isError(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$isError) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isEmpty(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$isEmpty) {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final <T extends java.lang.Object>T getDataOrNull(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$getDataOrNull) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final <T extends java.lang.Object>java.lang.String getErrorOrNull(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends T> $this$getErrorOrNull) {
        return null;
    }
}
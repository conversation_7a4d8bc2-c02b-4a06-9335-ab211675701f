package com.sdwu.kotlin.domain.usecase

import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.ECGRepository
import kotlinx.coroutines.flow.Flow

/**
 * ECG相关业务逻辑用例
 * Domain层：处理心电图数据的业务规则
 */
class ECGUseCase(private val ecgRepository: ECGRepository) {

    /**
     * 获取实时ECG数据流
     */
    fun getRealtimeECGDataStream(sessionId: String): Flow<ECGRealtimeData> {
        return ecgRepository.getRealtimeECGData(sessionId)
    }

    /**
     * 获取ECG波形数据
     */
    suspend fun getECGWaveformData(patientId: String): ECGWaveformData? {
        return ecgRepository.getECGWaveformData(patientId)
    }

    /**
     * 添加ECG数据点
     */
    suspend fun addECGDataPoint(dataPoint: ECGDataPoint): Boolean {
        return try {
            // 业务逻辑：验证数据点
            if (!isValidECGData(dataPoint)) {
                return false
            }

            // 注意：ECGRepository没有addECGData方法，这里需要调整
            // 暂时返回true，实际应该调用相应的存储方法
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 分析ECG数据
     */
    suspend fun analyzeECGData(waveformId: String): ECGAnalysisResult? {
        return try {
            ecgRepository.getAnalysisResult(waveformId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 开始ECG测量会话
     */
    suspend fun startMeasurementSession(patientId: String): String? {
        return try {
            ecgRepository.startMeasurementSession(patientId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 停止ECG测量会话
     */
    suspend fun stopMeasurementSession(sessionId: String): Boolean {
        return try {
            ecgRepository.stopMeasurementSession(sessionId)
        } catch (e: Exception) {
            false
        }
    }

    private fun isValidECGData(dataPoint: ECGDataPoint): Boolean {
        return dataPoint.voltage in -5.0..5.0 && dataPoint.timestamp > 0
    }

    private fun calculateHeartRate(data: List<ECGDataPoint>): Int {
        // 简化的心率计算
        return if (data.isNotEmpty()) {
            (60 + (0..40).random()) // 模拟心率 60-100
        } else 0
    }

    private fun analyzeRhythm(data: List<ECGDataPoint>): ECGRhythmType {
        return if (data.size > 100) ECGRhythmType.NORMAL_SINUS else ECGRhythmType.UNKNOWN
    }

    private fun assessDataQuality(data: List<ECGDataPoint>): ECGQuality {
        return when {
            data.isEmpty() -> ECGQuality.UNUSABLE
            data.size < 50 -> ECGQuality.POOR
            else -> ECGQuality.GOOD
        }
    }
}
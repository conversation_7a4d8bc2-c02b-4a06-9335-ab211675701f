package com.sdwu.kotlin.viewmodel

import android.util.Log
import com.sdwu.kotlin.data.model.User
import com.sdwu.kotlin.data.repository.UserRepositoryInterface
import com.sdwu.kotlin.presentation.base.BaseViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 个人资料ViewModel
 * 管理用户信息的业务逻辑和UI状态
 */
class ProfileViewModel(
    private val userUseCase: UserRepositoryInterface
) : BaseViewModel() {

    companion object {
        private const val TAG = "ProfileViewModel"
    }

    // UI状态
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    init {
        Log.d(TAG, "ProfileViewModel初始化开始")
        try {
            loadUserProfile()
            Log.d(TAG, "ProfileViewModel初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "ProfileViewModel初始化失败", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "初始化失败: ${e.message}"
            )
        }
    }
    
    /**
     * 加载用户资料
     */
    fun loadUserProfile() {
        Log.d(TAG, "开始加载用户资料")
        executeAsync(
            action = {
                Log.d(TAG, "尝试获取当前用户")
                val user = userUseCase.getUserById("default_user")
                Log.d(TAG, "获取到用户: ${user?.name ?: "null"}")

                _uiState.value = _uiState.value.copy(
                    user = user,
                    error = null
                )
                Log.d(TAG, "用户资料加载成功")
            }
        )
    }
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(name: String, email: String) {
        val currentUser = _uiState.value.user ?: return
        
        executeAsync(
            action = {
                val updatedUser = currentUser.copy(
                    name = name,
                    email = email
                )
                
                val success = userUseCase.updateUser(updatedUser)
                
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        user = updatedUser,
                        isEditMode = false
                    )
                } else {
                    throw Exception("更新用户信息失败")
                }
            }
        )
    }
    
    /**
     * 进入编辑模式
     */
    fun enterEditMode() {
        _uiState.value = _uiState.value.copy(isEditMode = true)
    }
    
    /**
     * 退出编辑模式
     */
    fun exitEditMode() {
        _uiState.value = _uiState.value.copy(isEditMode = false)
    }
    
    /**
     * 刷新用户信息
     */
    fun refreshUserInfo() {
        loadUserProfile()
    }
    
    /**
     * 清除错误状态
     */
    fun clearProfileError() {
        clearError() // 调用父类的clearError方法
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 验证用户输入
     */
    fun validateUserInput(name: String, email: String): String? {
        return when {
            name.isBlank() -> "用户名不能为空"
            email.isBlank() -> "邮箱不能为空"
            !email.contains("@") -> "邮箱格式不正确"
            else -> null
        }
    }
}

/**
 * 个人资料UI状态数据类
 */
data class ProfileUiState(
    val user: User? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isEditMode: Boolean = false
)

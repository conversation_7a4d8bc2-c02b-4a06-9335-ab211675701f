<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5BA517;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5A623;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1890B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D0021B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B71C1C;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    ViewSystemAdapter 工作原理与使用流程
  </text>
  
  <!-- BaseViewModel 区域 -->
  <g filter="url(#shadow)">
    <rect x="50" y="80" width="280" height="180" rx="10" fill="url(#blueGradient)" stroke="#2980b9" stroke-width="2"/>
    <text x="190" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      BaseViewModel
    </text>
    <text x="60" y="130" font-family="Arial, sans-serif" font-size="12" fill="white">
      • isLoading: StateFlow&lt;Boolean&gt;
    </text>
    <text x="60" y="150" font-family="Arial, sans-serif" font-size="12" fill="white">
      • error: StateFlow&lt;String?&gt;
    </text>
    <text x="60" y="170" font-family="Arial, sans-serif" font-size="12" fill="white">
      • executeAsync() 方法
    </text>
    <text x="60" y="190" font-family="Arial, sans-serif" font-size="12" fill="white">
      • clearError() 方法
    </text>
    <text x="60" y="210" font-family="Arial, sans-serif" font-size="12" fill="white">
      • 统一状态管理
    </text>
    <text x="60" y="230" font-family="Arial, sans-serif" font-size="12" fill="white">
      • 错误处理机制
    </text>
  </g>
  
  <!-- ViewSystemAdapter 核心区域 -->
  <g filter="url(#shadow)">
    <rect x="400" y="80" width="400" height="300" rx="10" fill="url(#greenGradient)" stroke="#27ae60" stroke-width="3"/>
    <text x="600" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      ViewSystemAdapter
    </text>
    <text x="410" y="125" font-family="Arial, sans-serif" font-size="12" fill="white">
      统一状态管理适配器 - 桥接Compose与传统View系统
    </text>
    
    <!-- 状态判断逻辑 -->
    <rect x="420" y="140" width="360" height="220" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="600" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      状态判断逻辑
    </text>
    
    <!-- 错误状态 -->
    <rect x="430" y="170" width="100" height="60" rx="5" fill="url(#redGradient)" stroke="#c0392b" stroke-width="1"/>
    <text x="480" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      error != null
    </text>
    <text x="480" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      显示ErrorView
    </text>
    <text x="480" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      提供重试功能
    </text>
    
    <!-- 加载状态 -->
    <rect x="550" y="170" width="100" height="60" rx="5" fill="url(#orangeGradient)" stroke="#e67e22" stroke-width="1"/>
    <text x="600" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      isLoading
    </text>
    <text x="600" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      显示LoadingView
    </text>
    <text x="600" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      加载指示器
    </text>
    
    <!-- 正常状态 -->
    <rect x="670" y="170" width="100" height="60" rx="5" fill="url(#blueGradient)" stroke="#3498db" stroke-width="1"/>
    <text x="720" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      else
    </text>
    <text x="720" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      显示content
    </text>
    <text x="720" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      正常内容
    </text>
    
    <!-- 核心代码示例 -->
    <text x="430" y="255" font-family="monospace" font-size="10" fill="white">
      when {
    </text>
    <text x="440" y="270" font-family="monospace" font-size="10" fill="white">
      error != null -&gt; ErrorView(...)
    </text>
    <text x="440" y="285" font-family="monospace" font-size="10" fill="white">
      isLoading -&gt; LoadingView()
    </text>
    <text x="440" y="300" font-family="monospace" font-size="10" fill="white">
      else -&gt; content(viewModel)
    </text>
    <text x="430" y="315" font-family="monospace" font-size="10" fill="white">
      }
    </text>
    
    <!-- 特性说明 -->
    <text x="430" y="340" font-family="Arial, sans-serif" font-size="11" fill="white">
      ✓ 自动状态监听  ✓ 统一错误处理  ✓ 简化UI逻辑
    </text>
  </g>
  
  <!-- UI组件区域 -->
  <g filter="url(#shadow)">
    <rect x="870" y="80" width="280" height="180" rx="10" fill="url(#orangeGradient)" stroke="#e67e22" stroke-width="2"/>
    <text x="1010" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      UI组件
    </text>
    <text x="880" y="130" font-family="Arial, sans-serif" font-size="12" fill="white">
      • LoadingView - 加载指示器
    </text>
    <text x="880" y="150" font-family="Arial, sans-serif" font-size="12" fill="white">
      • ErrorView - 错误显示与重试
    </text>
    <text x="880" y="170" font-family="Arial, sans-serif" font-size="12" fill="white">
      • Content - 业务内容组件
    </text>
    <text x="880" y="190" font-family="Arial, sans-serif" font-size="12" fill="white">
      • 统一视觉风格
    </text>
    <text x="880" y="210" font-family="Arial, sans-serif" font-size="12" fill="white">
      • Material3 设计
    </text>
    <text x="880" y="230" font-family="Arial, sans-serif" font-size="12" fill="white">
      • 响应式布局
    </text>
  </g>
  
  <!-- 使用示例区域 -->
  <g filter="url(#shadow)">
    <rect x="50" y="320" width="750" height="200" rx="10" fill="rgba(52, 73, 94, 0.9)" stroke="#34495e" stroke-width="2"/>
    <text x="425" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      使用示例代码
    </text>
    
    <text x="70" y="370" font-family="monospace" font-size="12" fill="#e74c3c">
      @Composable
    </text>
    <text x="70" y="385" font-family="monospace" font-size="12" fill="white">
      fun MyScreen() {
    </text>
    <text x="90" y="400" font-family="monospace" font-size="12" fill="#f39c12">
      val viewModel: MyViewModel = injectedViewModel { container -&gt;
    </text>
    <text x="110" y="415" font-family="monospace" font-size="12" fill="#f39c12">
      MyViewModel(container.userUseCase)
    </text>
    <text x="90" y="430" font-family="monospace" font-size="12" fill="#f39c12">
      }
    </text>
    <text x="90" y="450" font-family="monospace" font-size="12" fill="#2ecc71">
      ViewSystemAdapter(viewModel = viewModel) { vm -&gt;
    </text>
    <text x="110" y="465" font-family="monospace" font-size="12" fill="white">
      MyContent(viewModel = vm)
    </text>
    <text x="90" y="480" font-family="monospace" font-size="12" fill="#2ecc71">
      }
    </text>
    <text x="70" y="495" font-family="monospace" font-size="12" fill="white">
      }
    </text>
  </g>
  
  <!-- 优势特点区域 -->
  <g filter="url(#shadow)">
    <rect x="870" y="320" width="280" height="200" rx="10" fill="rgba(46, 204, 113, 0.9)" stroke="#27ae60" stroke-width="2"/>
    <text x="1010" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      核心优势
    </text>
    <text x="880" y="370" font-family="Arial, sans-serif" font-size="12" fill="white">
      🔄 统一状态管理
    </text>
    <text x="880" y="390" font-family="Arial, sans-serif" font-size="12" fill="white">
      🛡️ 自动错误处理
    </text>
    <text x="880" y="410" font-family="Arial, sans-serif" font-size="12" fill="white">
      ⚡ 简化开发流程
    </text>
    <text x="880" y="430" font-family="Arial, sans-serif" font-size="12" fill="white">
      🎨 一致的用户体验
    </text>
    <text x="880" y="450" font-family="Arial, sans-serif" font-size="12" fill="white">
      🔧 易于维护扩展
    </text>
    <text x="880" y="470" font-family="Arial, sans-serif" font-size="12" fill="white">
      📱 跨平台兼容
    </text>
    <text x="880" y="490" font-family="Arial, sans-serif" font-size="12" fill="white">
      🚀 提升开发效率
    </text>
  </g>
  
  <!-- 数据流箭头 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- BaseViewModel 到 ViewSystemAdapter -->
  <line x1="330" y1="170" x2="400" y2="170" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="365" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">
    状态流
  </text>
  
  <!-- ViewSystemAdapter 到 UI组件 -->
  <line x1="800" y1="170" x2="870" y2="170" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="835" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">
    渲染
  </text>
  
  <!-- 工作流程说明 -->
  <g filter="url(#shadow)">
    <rect x="50" y="560" width="1100" height="180" rx="10" fill="rgba(155, 89, 182, 0.9)" stroke="#8e44ad" stroke-width="2"/>
    <text x="600" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      ViewSystemAdapter 工作流程
    </text>
    
    <circle cx="120" cy="620" r="20" fill="#e74c3c" stroke="white" stroke-width="2"/>
    <text x="120" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">1</text>
    <text x="160" y="625" font-family="Arial, sans-serif" font-size="12" fill="white">
      ViewModel继承BaseViewModel，获得统一的状态管理能力
    </text>
    
    <circle cx="120" cy="660" r="20" fill="#f39c12" stroke="white" stroke-width="2"/>
    <text x="120" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">2</text>
    <text x="160" y="665" font-family="Arial, sans-serif" font-size="12" fill="white">
      ViewSystemAdapter监听ViewModel的isLoading和error状态
    </text>
    
    <circle cx="120" cy="700" r="20" fill="#27ae60" stroke="white" stroke-width="2"/>
    <text x="120" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">3</text>
    <text x="160" y="705" font-family="Arial, sans-serif" font-size="12" fill="white">
      根据状态自动选择显示LoadingView、ErrorView或业务内容
    </text>
    
    <circle cx="650" cy="620" r="20" fill="#3498db" stroke="white" stroke-width="2"/>
    <text x="650" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">4</text>
    <text x="690" y="625" font-family="Arial, sans-serif" font-size="12" fill="white">
      开发者只需关注业务逻辑，无需手动处理加载和错误状态
    </text>
    
    <circle cx="650" cy="660" r="20" fill="#9b59b6" stroke="white" stroke-width="2"/>
    <text x="650" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">5</text>
    <text x="690" y="665" font-family="Arial, sans-serif" font-size="12" fill="white">
      提供统一的用户体验和错误处理机制
    </text>
    
    <circle cx="650" cy="700" r="20" fill="#1abc9c" stroke="white" stroke-width="2"/>
    <text x="650" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">6</text>
    <text x="690" y="705" font-family="Arial, sans-serif" font-size="12" fill="white">
      支持Compose和传统View系统的无缝桥接
    </text>
  </g>
  
  <!-- 底部说明 -->
  <text x="600" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#7f8c8d">
    ViewSystemAdapter是连接ViewModel和UI的关键桥梁，实现了状态驱动的响应式UI架构
  </text>
</svg>

<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="pinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6EC7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9A9E;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC3F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#29B6F6;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#81C784;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#F8F9FA"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2C2C2C">
    HRV组件架构与扩展方案
  </text>
  
  <!-- 现有组件层 -->
  <g id="existing-components">
    <text x="50" y="80" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#E91E63">
      现有组件 (HRVComponents.kt)
    </text>
    
    <!-- HRVDataCard -->
    <rect x="50" y="100" width="200" height="80" rx="8" fill="url(#pinkGradient)" filter="url(#shadow)"/>
    <text x="150" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      HRVDataCard
    </text>
    <text x="150" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      首页数据卡片
    </text>
    <text x="150" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      RMSSD, SDNN, LF/HF
    </text>
    
    <!-- HRVMeasurementProgress -->
    <rect x="270" y="100" width="200" height="80" rx="8" fill="url(#pinkGradient)" filter="url(#shadow)"/>
    <text x="370" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      HRVMeasurementProgress
    </text>
    <text x="370" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      实时测量进度
    </text>
    <text x="370" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      进度条 + 实时数据
    </text>
    
    <!-- HRVMetricsOverviewCard -->
    <rect x="490" y="100" width="200" height="80" rx="8" fill="url(#pinkGradient)" filter="url(#shadow)"/>
    <text x="590" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      HRVMetricsOverviewCard
    </text>
    <text x="590" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      指标详解卡片
    </text>
    <text x="590" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      时域+频域+自主神经
    </text>
  </g>
  
  <!-- 数据模型层 -->
  <g id="data-models">
    <text x="50" y="230" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2196F3">
      数据模型 (HRVModels.kt)
    </text>
    
    <!-- HRVStats -->
    <rect x="50" y="250" width="150" height="60" rx="6" fill="url(#blueGradient)" filter="url(#shadow)"/>
    <text x="125" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      HRVStats
    </text>
    <text x="125" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      统计数据
    </text>
    <text x="125" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      压力/恢复水平
    </text>
    
    <!-- HRVTrendData -->
    <rect x="220" y="250" width="150" height="60" rx="6" fill="url(#blueGradient)" filter="url(#shadow)"/>
    <text x="295" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      HRVTrendData
    </text>
    <text x="295" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      趋势数据
    </text>
    <text x="295" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      7天变化趋势
    </text>
    
    <!-- HRVRealtimeData -->
    <rect x="390" y="250" width="150" height="60" rx="6" fill="url(#blueGradient)" filter="url(#shadow)"/>
    <text x="465" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      HRVRealtimeData
    </text>
    <text x="465" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      实时数据
    </text>
    <text x="465" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      测量进度+质量
    </text>
  </g>
  
  <!-- 扩展组件层 -->
  <g id="extension-components">
    <text x="750" y="80" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#4CAF50">
      扩展组件 (新增功能)
    </text>
    
    <!-- HRVSleepQualityCard -->
    <rect x="750" y="100" width="180" height="80" rx="8" fill="url(#greenGradient)" filter="url(#shadow)" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="840" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      HRVSleepQualityCard
    </text>
    <text x="840" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      睡眠质量分析
    </text>
    <text x="840" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      睡眠阶段+HRV关联
    </text>
    
    <!-- HRVStressMonitorCard -->
    <rect x="950" y="100" width="180" height="80" rx="8" fill="url(#greenGradient)" filter="url(#shadow)" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="1040" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      HRVStressMonitorCard
    </text>
    <text x="1040" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      压力监测
    </text>
    <text x="1040" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      实时压力评估
    </text>
  </g>
  
  <!-- 扩展数据模型 -->
  <g id="extension-models">
    <!-- HRVSleepData -->
    <rect x="750" y="250" width="150" height="60" rx="6" fill="url(#greenGradient)" filter="url(#shadow)" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="825" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      HRVSleepData
    </text>
    <text x="825" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      睡眠分析数据
    </text>
    <text x="825" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      睡眠阶段+质量评分
    </text>
    
    <!-- StressMonitorData -->
    <rect x="920" y="250" width="150" height="60" rx="6" fill="url(#greenGradient)" filter="url(#shadow)" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="995" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
      StressMonitorData
    </text>
    <text x="995" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      压力监测数据
    </text>
    <text x="995" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
      压力水平+趋势
    </text>
  </g>
  
  <!-- Repository层 -->
  <g id="repository-layer">
    <text x="50" y="380" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#FF5722">
      Repository层 (数据访问)
    </text>
    
    <rect x="50" y="400" width="1100" height="80" rx="8" fill="#FFF3E0" stroke="#FF5722" stroke-width="2" filter="url(#shadow)"/>
    <text x="600" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FF5722">
      HRVRepository
    </text>
    
    <!-- 现有方法 -->
    <text x="80" y="445" font-family="Arial, sans-serif" font-size="11" fill="#FF5722">
      现有方法: getHRVStats() | getHRVTrendData() | startHRVMeasurement()
    </text>
    
    <!-- 扩展方法 -->
    <text x="80" y="460" font-family="Arial, sans-serif" font-size="11" fill="#4CAF50">
      扩展方法: getHRVSleepAnalysis() | getStressAnalysis() | startStressMonitoring()
    </text>
  </g>
  
  <!-- ViewModel层 -->
  <g id="viewmodel-layer">
    <text x="50" y="530" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#9C27B0">
      ViewModel层 (状态管理)
    </text>
    
    <rect x="50" y="550" width="1100" height="100" rx="8" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2" filter="url(#shadow)"/>
    <text x="600" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9C27B0">
      HRVViewModel
    </text>
    
    <!-- 现有状态 -->
    <text x="80" y="595" font-family="Arial, sans-serif" font-size="11" fill="#9C27B0">
      现有状态: uiState (HRVStats + TrendData + Loading)
    </text>
    
    <!-- 扩展状态 -->
    <text x="80" y="610" font-family="Arial, sans-serif" font-size="11" fill="#4CAF50">
      扩展状态: stressMonitorState | sleepAnalysisState
    </text>
    
    <!-- 方法 -->
    <text x="80" y="625" font-family="Arial, sans-serif" font-size="11" fill="#9C27B0">
      方法: loadStats() | loadTrendData() | startStressMonitoring() | analyzeSleepQuality()
    </text>
  </g>
  
  <!-- 连接线 -->
  <!-- 组件到数据模型的连接 -->
  <line x1="150" y1="180" x2="125" y2="250" stroke="#E91E63" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="370" y1="180" x2="465" y2="250" stroke="#E91E63" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="590" y1="180" x2="295" y2="250" stroke="#E91E63" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 扩展组件到扩展数据模型的连接 -->
  <line x1="840" y1="180" x2="825" y2="250" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <line x1="1040" y1="180" x2="995" y2="250" stroke="#4CAF50" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- 数据模型到Repository的连接 -->
  <line x1="295" y1="310" x2="600" y2="400" stroke="#2196F3" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Repository到ViewModel的连接 -->
  <line x1="600" y1="480" x2="600" y2="550" stroke="#FF5722" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- 扩展说明框 -->
  <g id="extension-guide">
    <rect x="50" y="680" width="1100" height="100" rx="8" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" filter="url(#shadow)"/>
    <text x="600" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      扩展实现步骤
    </text>
    
    <text x="80" y="725" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      1. 在HRVModels.kt中定义新的数据结构 (如HRVSleepData, StressMonitorData)
    </text>
    <text x="80" y="740" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      2. 在HRVRepository中添加新的数据获取方法 (如getHRVSleepAnalysis, startStressMonitoring)
    </text>
    <text x="80" y="755" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      3. 在HRVViewModel中添加新的状态管理 (如stressMonitorState, sleepAnalysisState)
    </text>
    <text x="80" y="770" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      4. 在HRVComponents.kt中创建新的UI组件 (如HRVSleepQualityCard, HRVStressMonitorCard)
    </text>
  </g>
</svg>

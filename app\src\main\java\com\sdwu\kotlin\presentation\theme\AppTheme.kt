package com.sdwu.kotlin.presentation.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import com.sdwu.kotlin.di.AppContainer

/**
 * 全局主题提供者，支持依赖注入和主题管理
 */
val LocalAppContainer = staticCompositionLocalOf<AppContainer?> { null }

@Composable
fun AppTheme(
    appContainer: AppContainer,
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(
        LocalAppContainer provides appContainer
    ) {
        MaterialTheme {
            content()
        }
    }
}

/**
 * 获取AppContainer的便捷函数
 */
@Composable
fun getAppContainer(): AppContainer? {
    return LocalAppContainer.current
}
package com.sdwu.kotlin.presentation.adapter

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.sdwu.kotlin.presentation.base.BaseViewModel
import com.sdwu.kotlin.presentation.view.LoadingView
import com.sdwu.kotlin.presentation.view.ErrorView

/**
 * View系统适配器，统一处理状态管理
 * 支持Compose和View系统之间的桥接
 */
@Composable
fun <T : BaseViewModel> ViewSystemAdapter(
    viewModel: T,
    content: @Composable (T) -> Unit
) {
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    when {
        error != null -> {
            ErrorView(
                error = error!!,
                onRetry = { viewModel.clearError() }
            )
        }
        isLoading -> {
            LoadingView()
        }
        else -> {
            content(viewModel)
        }
    }
}
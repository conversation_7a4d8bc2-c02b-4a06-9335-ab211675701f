package com.sdwu.kotlin.utils

import android.content.Context
import com.sdwu.kotlin.KotlinApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * ProfileScreen测试工具
 * 用于测试个人资料页面的各个组件
 */
object ProfileScreenTest {
    
    private const val TAG = "ProfileScreenTest"
    
    /**
     * 测试ProfileScreen的所有依赖
     */
    fun testProfileScreenDependencies(context: Context) {
        ErrorLogger.logInfo(TAG, "=== 开始测试ProfileScreen依赖 ===")
        
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // 1. 测试Context转换
                testContextConversion(context)
                
                // 2. 测试AppContainer
                testAppContainer(context)
                
                // 3. 测试UserRepository
                testUserRepository(context)
                
                // 4. 测试数据库操作
                testDatabaseOperations(context)
                
                ErrorLogger.logInfo(TAG, "=== ProfileScreen依赖测试完成 ===")
                
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "ProfileScreen依赖测试失败", e)
            }
        }
    }
    
    /**
     * 测试Context转换
     */
    private fun testContextConversion(context: Context) {
        try {
            ErrorLogger.logDebug(TAG, "测试Context转换...")
            
            val applicationContext = context.applicationContext
            ErrorLogger.logDebug(TAG, "ApplicationContext类型: ${applicationContext.javaClass.simpleName}")
            
            val kotlinApp = applicationContext as? KotlinApplication
            if (kotlinApp != null) {
                ErrorLogger.logDebug(TAG, "✓ Context转换为KotlinApplication成功")
            } else {
                ErrorLogger.logError(TAG, "✗ Context转换为KotlinApplication失败")
                throw IllegalStateException("无法将Context转换为KotlinApplication")
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "Context转换测试失败", e)
            throw e
        }
    }
    
    /**
     * 测试AppContainer
     */
    private fun testAppContainer(context: Context) {
        try {
            ErrorLogger.logDebug(TAG, "测试AppContainer...")
            
            val app = context.applicationContext as KotlinApplication

            try {
                val container = app.appContainer
                ErrorLogger.logDebug(TAG, "✓ AppContainer可访问")
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "✗ AppContainer访问失败", e)
                throw e
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "AppContainer测试失败", e)
            throw e
        }
    }
    
    /**
     * 测试UserRepository
     */
    private fun testUserRepository(context: Context) {
        try {
            ErrorLogger.logDebug(TAG, "测试UserRepository...")
            
            val app = context.applicationContext as KotlinApplication
            val userRepository = app.appContainer.userRepository
            
            ErrorLogger.logDebug(TAG, "✓ UserRepository可访问")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "UserRepository测试失败", e)
            throw e
        }
    }
    
    /**
     * 测试数据库操作
     */
    private fun testDatabaseOperations(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                ErrorLogger.logDebug(TAG, "测试数据库操作...")
                
                val app = context.applicationContext as KotlinApplication
                val userRepository = app.appContainer.userRepository
                
                // 测试用户存在性检查
                ErrorLogger.logDebug(TAG, "测试用户存在性检查...")
                val userExists = userRepository.userExists("current_user")
                ErrorLogger.logDebug(TAG, "用户存在: $userExists")
                
                // 测试初始化默认用户
                ErrorLogger.logDebug(TAG, "测试初始化默认用户...")
                userRepository.initializeDefaultUser()
                ErrorLogger.logDebug(TAG, "✓ 默认用户初始化完成")
                
                // 测试获取当前用户
                ErrorLogger.logDebug(TAG, "测试获取当前用户...")
                val currentUser = userRepository.getCurrentUser()
                if (currentUser != null) {
                    ErrorLogger.logDebug(TAG, "✓ 获取当前用户成功: ${currentUser.name}")
                } else {
                    ErrorLogger.logWarning(TAG, "⚠ 当前用户为null")
                }
                
                ErrorLogger.logDebug(TAG, "✓ 数据库操作测试完成")
                
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "数据库操作测试失败", e)
            }
        }
    }
    
    /**
     * 模拟ProfileViewModel创建
     */
    fun testProfileViewModelCreation(context: Context) {
        try {
            ErrorLogger.logInfo(TAG, "=== 测试ProfileViewModel创建 ===")
            
            val app = context.applicationContext as KotlinApplication
            val userRepository = app.appContainer.userRepository
            
            // 模拟ViewModel创建过程
            ErrorLogger.logDebug(TAG, "创建ProfileViewModel...")
            val viewModel = com.sdwu.kotlin.viewmodel.ProfileViewModel(userRepository)
            ErrorLogger.logDebug(TAG, "✓ ProfileViewModel创建成功")
            
            // 测试UI状态
            val uiState = viewModel.uiState.value
            ErrorLogger.logDebug(TAG, "初始UI状态:")
            ErrorLogger.logDebug(TAG, "  isLoading: ${uiState.isLoading}")
            ErrorLogger.logDebug(TAG, "  error: ${uiState.error}")
            ErrorLogger.logDebug(TAG, "  user: ${uiState.user?.name ?: "null"}")
            ErrorLogger.logDebug(TAG, "  isEditMode: ${uiState.isEditMode}")
            
            ErrorLogger.logInfo(TAG, "=== ProfileViewModel创建测试完成 ===")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "ProfileViewModel创建测试失败", e)
        }
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests(context: Context) {
        ErrorLogger.logInfo(TAG, "=== 开始运行所有ProfileScreen测试 ===")
        
        try {
            testProfileScreenDependencies(context)
            testProfileViewModelCreation(context)
            
            ErrorLogger.logInfo(TAG, "=== 所有测试完成 ===")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "测试运行失败", e)
        }
    }
}

package com.sdwu.kotlin.presentation.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 基础ViewModel，提供公共功能
 * 包括加载状态和错误处理
 */
abstract class BaseViewModel : ViewModel() {
    
    protected val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    protected val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    protected fun executeAsync(
        action: suspend () -> Unit,
        onError: (Throwable) -> Unit = { _error.value = it.message }
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                action()
            } catch (e: Throwable) {
                onError(e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearError() {
        _error.value = null
    }
}
package com.sdwu.kotlin.screens;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001aX\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u00032\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0010H\u0003\u001a\u0010\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\fH\u0007\u00a8\u0006\u0013"}, d2 = {"ErrorScreen", "", "message", "", "onRetry", "Lkotlin/Function0;", "ProfileContent", "viewModel", "Lcom/sdwu/kotlin/viewmodel/ProfileViewModel;", "uiState", "Lcom/sdwu/kotlin/viewmodel/ProfileUiState;", "navController", "Landroidx/navigation/NavController;", "editName", "editEmail", "onEditNameChange", "Lkotlin/Function1;", "onEditEmailChange", "ProfileScreen", "app_debug"})
public final class ProfileScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProfileScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void ProfileContent(com.sdwu.kotlin.viewmodel.ProfileViewModel viewModel, com.sdwu.kotlin.viewmodel.ProfileUiState uiState, androidx.navigation.NavController navController, java.lang.String editName, java.lang.String editEmail, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEditNameChange, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEditEmailChange) {
    }
    
    /**
     * 错误显示组件
     */
    @androidx.compose.runtime.Composable()
    private static final void ErrorScreen(java.lang.String message, kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
}
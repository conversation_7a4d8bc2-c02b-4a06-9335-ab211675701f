package com.sdwu.kotlin.presentation.components;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a-\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00032\u0011\u0010\t\u001a\r\u0012\u0004\u0012\u00020\u00010\n\u00a2\u0006\u0002\b\u000b2\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a_\u0010\f\u001a\u00020\u0001\"\u0004\b\u0000\u0010\r2\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\r0\u00100\u000f2\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u0017\u0010\u0013\u001a\u0013\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u00020\u00010\u0014\u00a2\u0006\u0002\b\u000bH\u0007\u00a8\u0006\u0015"}, d2 = {"DataItem", "", "label", "", "value", "modifier", "Landroidx/compose/ui/Modifier;", "InfoCard", "title", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "StateAwareListView", "T", "uiState", "Lcom/sdwu/kotlin/presentation/state/UiState;", "", "onRetry", "emptyMessage", "itemContent", "Lkotlin/Function1;", "app_debug"})
public final class CommonComponentsKt {
    
    /**
     * 通用列表组件，支持自动状态管理
     */
    @androidx.compose.runtime.Composable()
    public static final <T extends java.lang.Object>void StateAwareListView(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.presentation.state.UiState<? extends java.util.List<? extends T>> uiState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    java.lang.String emptyMessage, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, kotlin.Unit> itemContent) {
    }
    
    /**
     * 通用卡片组件
     */
    @androidx.compose.runtime.Composable()
    public static final void InfoCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 数据显示组件
     */
    @androidx.compose.runtime.Composable()
    public static final void DataItem(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}
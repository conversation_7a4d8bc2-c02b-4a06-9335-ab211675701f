!com.sdwu.kotlin.KotlinApplicationcom.sdwu.kotlin.MainActivity&com.sdwu.kotlin.data.model.ECGLeadType%com.sdwu.kotlin.data.model.ECGQuality(com.sdwu.kotlin.data.model.ECGRhythmType*com.sdwu.kotlin.data.model.HRVAnalysisType/com.sdwu.kotlin.data.model.HRVMeasurementStatus6com.sdwu.kotlin.data.repository.InMemoryUserRepository"com.sdwu.kotlin.utils.CrashHandler)com.sdwu.kotlin.viewmodel.DetailViewModel&com.sdwu.kotlin.viewmodel.ECGViewModel&com.sdwu.kotlin.viewmodel.HRVViewModel'com.sdwu.kotlin.viewmodel.HomeViewModel*com.sdwu.kotlin.viewmodel.ProfileViewModel+com.sdwu.kotlin.viewmodel.SettingsViewModel/com.sdwu.kotlin.presentation.base.BaseViewModel2com.sdwu.kotlin.presentation.state.UiState.Loading2com.sdwu.kotlin.presentation.state.UiState.Success0com.sdwu.kotlin.presentation.state.UiState.Error0com.sdwu.kotlin.presentation.state.UiState.Empty                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
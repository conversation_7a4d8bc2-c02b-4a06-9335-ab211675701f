package com.sdwu.kotlin.presentation.util;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001aO\u0010\u0000\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u00022\u001c\u0010\u0003\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u00042\u0019\u0010\u0007\u001a\u0015\u0012\u0006\u0012\u0004\u0018\u0001H\u0002\u0012\u0004\u0012\u00020\u00010\u0004\u00a2\u0006\u0002\b\bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\t\u001a3\u0010\n\u001a\u0002H\u0002\"\n\b\u0000\u0010\u0002\u0018\u0001*\u00020\u000b2\u0014\b\u0004\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u0002H\u00020\u0004H\u0087\b\u00f8\u0001\u0001\u00a2\u0006\u0002\u0010\u000e\u001aB\u0010\u000f\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0010\"\u0004\b\u0000\u0010\u00022\u0006\u0010\u0011\u001a\u0002H\u00022\u001c\u0010\u0012\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u0004H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0013\u0082\u0002\u000b\n\u0002\b\u0019\n\u0005\b\u009920\u0001\u00a8\u0006\u0014"}, d2 = {"LazyLoadState", "", "T", "loader", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "", "content", "Landroidx/compose/runtime/Composable;", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "injectedViewModel", "Landroidx/lifecycle/ViewModel;", "factory", "Lcom/sdwu/kotlin/di/AppContainer;", "(Lkotlin/jvm/functions/Function1;)Landroidx/lifecycle/ViewModel;", "rememberAsyncState", "Landroidx/compose/runtime/State;", "initialValue", "operation", "(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Landroidx/compose/runtime/State;", "app_debug"})
public final class ComposeUtilsKt {
    
    /**
     * 状态管理工具
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final <T extends java.lang.Object>androidx.compose.runtime.State<T> rememberAsyncState(T initialValue, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super T>, ? extends java.lang.Object> operation) {
        return null;
    }
    
    /**
     * 延迟加载工具
     */
    @androidx.compose.runtime.Composable()
    public static final <T extends java.lang.Object>void LazyLoadState(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super T>, ? extends java.lang.Object> loader, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, kotlin.Unit> content) {
    }
}
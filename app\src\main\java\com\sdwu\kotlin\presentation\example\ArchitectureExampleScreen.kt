package com.sdwu.kotlin.presentation.example

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.sdwu.kotlin.presentation.adapter.ViewSystemAdapter
import com.sdwu.kotlin.presentation.components.InfoCard
import com.sdwu.kotlin.presentation.components.DataItem
import com.sdwu.kotlin.presentation.components.StateAwareListView
import com.sdwu.kotlin.presentation.state.UiState
import com.sdwu.kotlin.presentation.util.injectedViewModel
import com.sdwu.kotlin.viewmodel.ProfileViewModel

/**
 * 完整的架构使用示例
 * 演示如何使用新的分层架构和View系统
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArchitectureExampleScreen() {
    // 使用依赖注入创建ViewModel
    val viewModel: ProfileViewModel = injectedViewModel { container ->
        ProfileViewModel(container.userUseCase)
    }
    
    // 使用ViewSystemAdapter统一处理状态
    ViewSystemAdapter(viewModel = viewModel) { vm ->
        ExampleContent(viewModel = vm)
    }
}

@Composable
private fun ExampleContent(viewModel: ProfileViewModel) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "架构示例页面",
            style = MaterialTheme.typography.headlineMedium
        )
        
        // 用户信息卡片
        InfoCard(
            title = "用户信息",
            content = {
                uiState.user?.let { user ->
                    DataItem(label = "姓名", value = user.name)
                    Spacer(modifier = Modifier.height(8.dp))
                    DataItem(label = "邮箱", value = user.email)
                } ?: run {
                    Text("暂无用户信息")
                }
            }
        )
        
        // 操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { viewModel.loadUserProfile() },
                modifier = Modifier.weight(1f)
            ) {
                Text("刷新数据")
            }
            
            Button(
                onClick = { viewModel.enterEditMode() },
                modifier = Modifier.weight(1f),
                enabled = uiState.user != null
            ) {
                Text("编辑模式")
            }
        }
        
        // 状态信息
        InfoCard(
            title = "状态信息",
            content = {
                DataItem(
                    label = "编辑模式",
                    value = if (uiState.isEditMode) "是" else "否"
                )
                Spacer(modifier = Modifier.height(8.dp))
                DataItem(
                    label = "用户存在",
                    value = if (uiState.user != null) "是" else "否"
                )
            }
        )
        
        // 使用StateAwareListView的示例
        InfoCard(
            title = "列表示例",
            content = {
                val listState = remember(uiState.user) {
                    if (uiState.user != null) {
                        UiState.Success(listOf(uiState.user!!))
                    } else {
                        UiState.Empty
                    }
                }

                StateAwareListView(
                    uiState = listState,
                    onRetry = { viewModel.loadUserProfile() },
                    emptyMessage = "暂无用户数据",
                    modifier = Modifier.height(200.dp)
                ) { user ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = user.name,
                                style = MaterialTheme.typography.titleSmall
                            )
                            Text(
                                text = user.email,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        )
    }
}